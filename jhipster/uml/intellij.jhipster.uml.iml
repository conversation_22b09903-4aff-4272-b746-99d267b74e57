<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/resources" type="java-resource" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="module" module-name="intellij.jhipster" />
    <orderEntry type="module" module-name="intellij.diagram" />
    <orderEntry type="module" module-name="intellij.platform.core" />
    <orderEntry type="module" module-name="intellij.platform.util" />
    <orderEntry type="module" module-name="intellij.platform.editor.ui" />
    <orderEntry type="module" module-name="intellij.platform.ide.core" />
    <orderEntry type="module" module-name="intellij.platform.core.ui" />
    <orderEntry type="module" module-name="intellij.platform.ide.impl" />
    <orderEntry type="module" module-name="intellij.platform.analysis" />
    <orderEntry type="module" module-name="intellij.diagram.impl" />
    <orderEntry type="module" module-name="intellij.platform.core.impl" />
  </component>
</module>