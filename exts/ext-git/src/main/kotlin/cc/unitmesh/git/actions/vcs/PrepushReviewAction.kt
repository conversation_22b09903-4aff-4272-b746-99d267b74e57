package cc.unitmesh.git.actions.vcs

import cc.unitmesh.diagram.editor.mermaid.MermaidPreviewFileEditor
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.popup.JBPopupFactory
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.vcs.changes.ChangeListManager
import com.intellij.testFramework.LightVirtualFile
import com.intellij.ui.components.JBScrollPane
import com.intellij.util.ui.JBUI
import java.awt.BorderLayout
import java.awt.Dimension
import javax.swing.JPanel

class PrepushReviewAction : AnAction() {
    override fun actionPerformed(event: AnActionEvent) {
        val project = event.project ?: return

        // 获取当前的变更
        val changeListManager = ChangeListManager.getInstance(project)
        val changes = changeListManager.allChanges.toList()

        if (changes.isEmpty()) {
            // 如果没有变更，显示提示
            showNoChangesDialog(project)
            return
        }

        // 生成结构变化图
        val structureDiagramBuilder = StructureDiagramBuilder(project, changes)
        val mermaidContent = structureDiagramBuilder.build()

        // 显示弹窗
        showMermaidDiagramPopup(project, mermaidContent)
    }

    private fun showNoChangesDialog(project: Project) {
        val panel = JPanel(BorderLayout()).apply {
            preferredSize = Dimension(300, 100)
            border = JBUI.Borders.empty(16)
            add(javax.swing.JLabel("No changes detected for review."), BorderLayout.CENTER)
        }

        JBPopupFactory.getInstance()
            .createComponentPopupBuilder(panel, null)
            .setTitle("Prepush Review")
            .setResizable(false)
            .setMovable(true)
            .setRequestFocus(true)
            .setCancelOnClickOutside(true)
            .createPopup()
            .showCenteredInCurrentWindow(project)
    }

    private fun showMermaidDiagramPopup(project: Project, mermaidContent: String) {
        ApplicationManager.getApplication().invokeLater {
            // 创建一个临时的 Mermaid 文件
            val tempFile = LightVirtualFile("structure_changes.mermaid", mermaidContent)

            // 创建 MermaidPreviewFileEditor
            val mermaidEditor = MermaidPreviewFileEditor(project, tempFile)

            val scrollPane = JBScrollPane(mermaidEditor.component).apply {
                preferredSize = Dimension(800, 600)
                border = JBUI.Borders.empty()
            }

            val mainPanel = JPanel(BorderLayout()).apply {
                border = JBUI.Borders.empty(8)
                add(scrollPane, BorderLayout.CENTER)
            }

            val popup = JBPopupFactory.getInstance()
                .createComponentPopupBuilder(mainPanel, mermaidEditor.component)
                .setTitle("Code Structure Changes Review")
                .setResizable(true)
                .setMovable(true)
                .setRequestFocus(true)
                .setCancelOnClickOutside(true)
                .setCancelOnOtherWindowOpen(true)
                .setMinSize(Dimension(600, 400))
                .createPopup()

            // 确保在弹窗关闭时清理资源
            popup.addListener(object : com.intellij.openapi.ui.popup.JBPopupListener {
                override fun onClosed(event: com.intellij.openapi.ui.popup.LightweightWindowEvent) {
                    Disposer.dispose(mermaidEditor)
                }
            })

            // 显示弹窗
            popup.showCenteredInCurrentWindow(project)
        }
    }
}
