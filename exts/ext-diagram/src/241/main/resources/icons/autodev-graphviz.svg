<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 256 256">
    <defs>
        <linearGradient id="g1" x1="0" x2="1" y1="0" y2="1">
            <stop offset="0" stop-color="#FFB86B"/>
            <stop offset="1" stop-color="#FF6B8A"/>
        </linearGradient>
        <linearGradient id="g2" x1="0" x2="1" y1="0" y2="1">
            <stop offset="0" stop-color="#6BCBFF"/>
            <stop offset="1" stop-color="#6B8AFF"/>
        </linearGradient>
        <marker id="arrow" markerWidth="6" markerHeight="6" refX="5" refY="3" orient="auto" markerUnits="strokeWidth">
            <path d="M0 0 L6 3 L0 6 z" fill="#222" />
        </marker>
    </defs>
    <rect x="12" y="12" width="232" height="232" rx="34" ry="34" fill="#48A178"/>
    <g stroke="#222" stroke-width="6" stroke-linecap="round" stroke-linejoin="round" fill="none" marker-end="url(#arrow)" opacity="0.95">
        <path d="M72 168 C92 138, 120 120, 140 108"/>
        <path d="M150 110 C180 100, 210 130, 190 156"/>
        <path d="M180 160 C150 188, 110 200, 82 176"/>
    </g>
    <g transform="translate(64,168)">
        <circle cx="0" cy="0" r="20" fill="#fff" stroke="#222" stroke-width="4"/>
        <circle cx="-1" cy="-1" r="12" fill="url(#g2)" />
    </g>
    <g transform="translate(140,108)">
        <circle cx="0" cy="0" r="26" fill="#fff" stroke="#222" stroke-width="5"/>
        <circle cx="0" cy="0" r="16" fill="url(#g1)"/>
    </g>
    <g transform="translate(188,156)">
        <circle cx="0" cy="0" r="22" fill="#fff" stroke="#222" stroke-width="4"/>
        <circle cx="0" cy="0" r="14" fill="#FFEFEF" />
        <circle cx="-2" cy="-1" r="10" fill="url(#g2)" opacity="0.95"/>
    </g>
</svg>
